<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import {
    projectManagementService,
    type ProjectQuery,
    type ProjectWithDetails,
    type ProjectPersonnelWithDetails
  } from '$lib/services/projectManagementService';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import StatsBadge from "$lib/components/project/StatsBadge.svelte";
  import ExportProjectsDialog from "$lib/components/project/ExportProjectsDialog.svelte";
  import FilterConfigDialog from "$lib/components/filter/FilterConfigDialog.svelte";
  import QuickFilterTag from "$lib/components/filter/QuickFilterTag.svelte";

  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import { staffService } from '$lib/services/staffService';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import { ruleDesignerService } from '$lib/services/ruleDesignerService';
  import { goto } from '$app/navigation';
  import { PlusCircle, Search, Pencil, Trash, RefreshCw, FileDown, Folder, ClipboardList, Upload, AlertTriangle, X, Eye, MoreHorizontal, Star, Building2, CircleSlash } from 'lucide-svelte';
  import { saveFilterState } from '$lib/stores/filterStore';
  import CsvImportDialog from '$lib/components/csv-import/CsvImportDialog.svelte';
  import { filterConfigService } from '$lib/services/filterConfigService';
  import type { FilterConfig, QuickFilterTag as QuickFilterTagType } from '$lib/types/filterConfig';

  type ProjectCardView = ProjectWithDetails & { startDays: number | null };

  const PIN_LIMIT = 10;
  const STAFF_STORAGE_KEY = 'projectPinStaffId';

  // 状态管理
  let projects = $state<ProjectCardView[]>([]);
  let allProjects = $state<ProjectCardView[]>([]); // 存储所有项目，用于统计
  let isLoading = $state(false);
  let isDeleting = $state(false);
  let error = $state<string | null>(null);
  let total = $state(0);
  let totalAllProjects = $state(0); // 所有项目的总数
  let currentPage = $state(1);
  let pageSize = $state(50);
  let showDeleteConfirm = $state(false);
  let projectToDelete = $state<{id: string, name: string} | null>(null);
  let confirmProjectName = $state('');
  let deleteConfirmError = $state<string | null>(null);
  let showExportDialog = $state(false); // 控制导出对话框显示
  let showCsvImportDialog = $state(false); // 控制CSV导入对话框显示
  let showFilterConfigDialog = $state(false); // 控制筛选配置对话框显示

  // 筛选配置相关状态
  let filterConfigs = $state<FilterConfig[]>([]);
  let currentFilterConfig = $state<FilterConfig | null>(null);
  let customFilterTags = $state<QuickFilterTagType[]>([]);

  let activeStaffId = $state<number | null>(null);
  let onlyPinned = $state(false);
  let pinProcessing = $state<Record<string, boolean>>({});
  let pinError = $state<string | null>(null);

  let pinnedProjects = $derived(projects.filter(project => !!project.project.is_pinned));
  let pinnedCount = $derived(pinnedProjects.length);
  let pinnedLimitReached = $derived(pinnedCount >= PIN_LIMIT);

  // 入排标准缓存
  let criteriaCache = $state<Map<string, any[]>>(new Map());
  let showCriteriaModal = $state(false);
  let criteriaModalLoading = $state(false);
  let criteriaModalError = $state<string | null>(null);
  let criteriaModalProjectName = $state('');
  let criteriaModalItems = $state<any[]>([]);
  let criteriaModalProjectId = $state<string | null>(null);
  let actionMenuProjectId = $state<string | null>(null);

  // 当前活动的过滤器
  let activeFilter = $state<'all' | 'ongoing' | 'finished' | 'recruiting'>('ongoing');

  // 快捷筛选编辑模式
  let editMode = $state(false);

  // 项目统计
  // 排序相关函数
  function moveTagUp(tag: QuickFilterTagType) {
    const currentIndex = customFilterTags.findIndex(t => t.id === tag.id);
    if (currentIndex > 0) {
      const newOrder = [...customFilterTags];
      // 交换当前项和前一项的位置
      [newOrder[currentIndex - 1], newOrder[currentIndex]] = [newOrder[currentIndex], newOrder[currentIndex - 1]];
      customFilterTags = newOrder;
      saveTagOrder();
    }
  }

  function moveTagDown(tag: QuickFilterTagType) {
    const currentIndex = customFilterTags.findIndex(t => t.id === tag.id);
    if (currentIndex < customFilterTags.length - 1) {
      const newOrder = [...customFilterTags];
      // 交换当前项和后一项的位置
      [newOrder[currentIndex], newOrder[currentIndex + 1]] = [newOrder[currentIndex + 1], newOrder[currentIndex]];
      customFilterTags = newOrder;
      saveTagOrder();
    }
  }

  function saveTagOrder() {
    try {
      const orderIds = customFilterTags.map(t => t.id);
      localStorage.setItem('customFilterTagsOrder', JSON.stringify(orderIds));
      console.log('[排序] 保存新顺序到localStorage:', orderIds);
    } catch (e) {
      console.warn('[排序] 保存本地排序失败:', e);
    }
  }
  let projectStats = $state({
    totalProjects: 0,
    ongoingProjects: 0,
    finishedProjects: 0,
    recruitingProjects: 0,
    diseaseDistribution: [] as {disease: string, count: number}[],
    stageDistribution: [] as {stage: string, count: number}[],
    // 各状态下的疾病分布
    allDiseaseDistribution: [] as {disease: string, count: number}[],
    ongoingDiseaseDistribution: [] as {disease: string, count: number}[],
    finishedDiseaseDistribution: [] as {disease: string, count: number}[],
    recruitingDiseaseDistribution: [] as {disease: string, count: number}[]
  });

  // 筛选条件
  let searchName = $state('');
  let selectedDiseaseId = $state<number | null>(null);
  let selectedStageId = $state<number | null>(null);
  let selectedStatusId = $state<number | null>(null);
  let selectedRecruitmentStatusId = $state<number | null>(null);
  let sortBy = $state('project_short_name');
  let sortOrder = $state<'asc' | 'desc'>('desc');

  // 字典项
  let diseases = $state<{item_id: number, item_value: string}[]>([]);
  let stages = $state<{item_id: number, item_value: string}[]>([]);
  let statuses = $state<{item_id: number, item_value: string}[]>([]);
  let recruitmentStatuses = $state<{item_id: number, item_value: string}[]>([]);
  let sponsors = $state<{item_id: number, item_value: string}[]>([]);
  let piStaff = $state<{id: number, name: string}[]>([]);

  // 额外筛选（申办方、PI）
  let selectedSponsorIds = $state<string[]>([]);
  let selectedPiIds = $state<string[]>([]);

  // 加载所有项目（用于统计）
  async function loadAllProjects() {
    try {
      // 构建查询参数，不包含任何筛选条件
      const query: ProjectQuery = {
        page: 1,
        page_size: 1000, // 设置较大的页面大小以获取所有项目
        sort_by: 'project_name',
        sort_order: 'asc'
      };

      // 调用服务获取所有项目列表
      const result = await projectManagementService.getProjects(query);

      // 获取每个项目的详细信息，包括研究人员和申办方
      const projectsWithDetails = await Promise.all(
        result.items.map(async (project) => {
          if (project.project.project_id) {
            try {
              // 获取项目详情
              const details = await projectManagementService.getProjectDetails(project.project.project_id);
              if (details) {
                // 如果成功获取详情，更新人员信息和申办方信息
                return {
                  ...project,
                  personnel: details.personnel || [],
                  sponsors: details.sponsors || []
                };
              }
            } catch (error) {
              console.error(`获取项目 ${project.project.project_id} 详情失败:`, error);
            }
          }
          return project;
        })
      );

      allProjects = mapWithStartDays(projectsWithDetails);
      totalAllProjects = result.total;

      // 更新项目统计（基于所有项目）
      updateAllProjectStats();
    } catch (err) {
      console.error('加载所有项目失败:', err);
    }
  }

  // 加载筛选后的项目列表
  async function loadProjects() {
    isLoading = true;
    error = null;

    try {
      const staffId = activeStaffId;

      if (onlyPinned && staffId === null) {
        projects = [];
        total = 0;
        pinError = '当前没有可用的项目人员，无法查看置顶项目';
        return;
      }

      if (!onlyPinned) {
        pinError = null;
      }

      const sponsorIds = selectedSponsorIds
        .map(id => Number(id))
        .filter(id => !Number.isNaN(id));
      const piIds = selectedPiIds
        .map(id => Number(id))
        .filter(id => !Number.isNaN(id));

      // 构建查询参数
      const query: ProjectQuery = {
        name: searchName || undefined,
        disease_item_id: selectedDiseaseId || undefined,
        project_stage_item_id: selectedStageId || undefined,
        project_status_item_id: selectedStatusId || undefined,
        recruitment_status_item_id: selectedRecruitmentStatusId || undefined,
        sponsor_item_ids: sponsorIds.length > 0 ? sponsorIds : undefined,
        pi_personnel_ids: piIds.length > 0 ? piIds : undefined,
        staff_id: staffId ?? undefined,
        only_pinned: onlyPinned && staffId !== null ? true : undefined,
        include_pinned: staffId !== null ? true : undefined,
        page: currentPage,
        page_size: pageSize,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      console.log('[快捷筛选] loadProjects 使用查询参数:', JSON.stringify(query));

      // 调用服务获取项目列表
      const result = await projectManagementService.getProjects(query);

      // 获取每个项目的详细信息，包括研究人员和申办方
      const projectsWithDetails = await Promise.all(
        result.items.map(async (project) => {
          if (project.project.project_id) {
            try {
              // 获取项目详情
              const details = await projectManagementService.getProjectDetails(project.project.project_id);
              if (details) {
                // 如果成功获取详情，更新人员信息和申办方信息
                return {
                  ...project,
                  personnel: details.personnel || [],
                  sponsors: details.sponsors || []
                };
              }
            } catch (error) {
              console.error(`获取项目 ${project.project.project_id} 详情失败:`, error);
            }
          }
          return project;
        })
      );

      projects = mapWithStartDays(projectsWithDetails);
      total = result.total;
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isLoading = false;
    }
  }

  function resolveStoredStaffId(staffList: { id: number }[]): number | null {
    try {
      const stored = localStorage.getItem(STAFF_STORAGE_KEY);
      if (stored) {
        const parsed = Number(stored);
        if (!Number.isNaN(parsed) && staffList.some(staff => staff.id === parsed)) {
          return parsed;
        }
      }
    } catch (err) {
      console.warn('读取置顶设置失败:', err);
    }

    return staffList.length > 0 ? staffList[0].id : null;
  }

  function setActiveStaffId(id: number | null, options: { silent?: boolean } = {}) {
    activeStaffId = id;

    try {
      if (id !== null) {
        localStorage.setItem(STAFF_STORAGE_KEY, String(id));
      } else {
        localStorage.removeItem(STAFF_STORAGE_KEY);
      }
    } catch (err) {
      console.warn('保存置顶设置失败:', err);
    }

    pinError = null;

    if (!options.silent) {
      applyFilters();
    }
  }

  function togglePinnedFilter() {
    if (activeStaffId === null) {
      pinError = '当前没有可用的项目人员，无法使用置顶筛选';
      onlyPinned = false;
      return;
    }

    onlyPinned = !onlyPinned;
    pinError = null;
    applyFilters({ resetPage: true });
  }

  function isPinBusy(projectId?: string | null): boolean {
    if (!projectId) return false;
    return Boolean(pinProcessing[projectId]);
  }

  async function handleTogglePin(event: MouseEvent, project: ProjectCardView) {
    event.stopPropagation();
    event.preventDefault();

    const projectId = project?.project?.project_id;
    if (!projectId) return;

    if (activeStaffId === null) {
      pinError = '当前没有可用的项目人员，无法置顶项目';
      return;
    }

    const currentlyPinned = Boolean(project.project.is_pinned);
    const nextState = !currentlyPinned;

    if (nextState && pinnedCount >= PIN_LIMIT) {
      pinError = `最多只能置顶 ${PIN_LIMIT} 个项目`;
      return;
    }

    pinError = null;
    pinProcessing = { ...pinProcessing, [projectId]: true };

    try {
      const nextRank = nextState
        ? (project.project.pinned_rank ?? pinnedProjects.length + 1)
        : undefined;

      await projectManagementService.toggleProjectPin(projectId, activeStaffId, nextState, nextRank);
      await loadProjects();
    } catch (err) {
      let message = err instanceof Error ? err.message : String(err);
      message = message.replace(/^更新项目置顶状态失败:\s*/, '');
      pinError = message || '更新项目置顶状态失败';
    } finally {
      const { [projectId]: _, ...rest } = pinProcessing;
      pinProcessing = rest;
    }
  }

  // 更新所有项目的统计信息（基于allProjects）
  function updateAllProjectStats() {
    if (!allProjects || allProjects.length === 0) return;

    // 计算基本统计
    projectStats.totalProjects = totalAllProjects;

    // 计算在研项目数量（项目状态为"在研"的项目）
    const ongoingProjects = allProjects.filter(p =>
      p.project_status?.item_value === '在研'
    );
    projectStats.ongoingProjects = ongoingProjects.length;

    // 计算已结束项目数量（项目状态为"已结束"的项目）
    const finishedProjects = allProjects.filter(p =>
      p.project_status?.item_value === '已结束'
    );
    projectStats.finishedProjects = finishedProjects.length;

    // 计算招募中项目数量
    const recruitingProjects = allProjects.filter(p =>
      p.recruitment_status?.item_value === '招募中'
    );
    projectStats.recruitingProjects = recruitingProjects.length;

    // 计算所有项目的疾病分布
    const diseaseMap = new Map<string, number>();
    allProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = diseaseMap.get(p.disease.item_value) || 0;
        diseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.diseaseDistribution = Array.from(diseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算所有项目的疾病分布（用于"总项目数"过滤器）
    projectStats.allDiseaseDistribution = [...projectStats.diseaseDistribution];

    // 计算在研项目的疾病分布
    const ongoingDiseaseMap = new Map<string, number>();
    ongoingProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = ongoingDiseaseMap.get(p.disease.item_value) || 0;
        ongoingDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.ongoingDiseaseDistribution = Array.from(ongoingDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算已结束项目的疾病分布
    const finishedDiseaseMap = new Map<string, number>();
    finishedProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = finishedDiseaseMap.get(p.disease.item_value) || 0;
        finishedDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.finishedDiseaseDistribution = Array.from(finishedDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算招募中项目的疾病分布
    const recruitingDiseaseMap = new Map<string, number>();
    recruitingProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = recruitingDiseaseMap.get(p.disease.item_value) || 0;
        recruitingDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.recruitingDiseaseDistribution = Array.from(recruitingDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算阶段分布
    const stageMap = new Map<string, number>();
    allProjects.forEach(p => {
      if (p.project_stage?.item_value) {
        const count = stageMap.get(p.project_stage.item_value) || 0;
        stageMap.set(p.project_stage.item_value, count + 1);
      }
    });
    projectStats.stageDistribution = Array.from(stageMap.entries())
      .map(([stage, count]) => ({ stage, count }))
      .sort((a, b) => b.count - a.count);

    // 更新快捷筛选标签的数量
    updateCustomFilterTagsWithCounts();
  }

  // 为快捷筛选标签计算数量
  async function updateCustomFilterTagsWithCounts() {
    if (!customFilterTags || customFilterTags.length === 0) return;

    const updatedTags = await Promise.all(
      customFilterTags.map(async (tag) => {
        if (!tag.filterConfig) return tag;

        try {
          // 根据筛选配置计算项目数量
          const count = calculateProjectCountForFilter(tag.filterConfig);
          return {
            ...tag,
            count
          };
        } catch (error) {
          console.error(`计算筛选标签 "${tag.label}" 的数量失败:`, error);
          return tag;
        }
      })
    );

    customFilterTags = updatedTags;
  }

  // 根据筛选配置计算项目数量
  function calculateProjectCountForFilter(config: FilterConfig): number {
    if (!allProjects || allProjects.length === 0) return 0;

    return allProjects.filter(project => {
      return config.conditions.every(condition => {
        switch (condition.field) {
          case 'project_status_item_id':
            return condition.operator === 'equals' &&
                   project.project_status?.item_id === condition.value;

          case 'recruitment_status_item_id':
            return condition.operator === 'equals' &&
                   project.recruitment_status?.item_id === condition.value;

          case 'disease_item_id':
            return condition.operator === 'equals' &&
                   project.disease?.item_id === condition.value;

          case 'project_stage_item_id':
            return condition.operator === 'equals' &&
                   project.project_stage?.item_id === condition.value;

          case 'sponsor_item_ids':
            if (condition.operator === 'in' && Array.isArray(condition.value)) {
              return project.sponsors?.some(sponsor =>
                condition.value.includes(sponsor.sponsor_item_id)
              ) || false;
            } else if (condition.operator === 'equals' && Array.isArray(condition.value)) {
              // 处理单个值的情况
              return project.sponsors?.some(sponsor =>
                condition.value.includes(sponsor.sponsor_item_id)
              ) || false;
            }
            return false;

          case 'pi_personnel_ids':
            if (condition.operator === 'in' && Array.isArray(condition.value)) {
              return project.personnel?.some(person =>
                condition.value.includes(person.personnel_id) &&
                person.role?.item_value?.includes('主要研究者')
              ) || false;
            } else if (condition.operator === 'equals' && Array.isArray(condition.value)) {
              // 处理单个值的情况
              return project.personnel?.some(person =>
                condition.value.includes(person.personnel_id) &&
                person.role?.item_value?.includes('主要研究者')
              ) || false;
            }
            return false;

          case 'project_name':
            if (condition.operator === 'contains') {
              const searchTerm = condition.value?.toLowerCase() || '';
              return project.project.project_name?.toLowerCase().includes(searchTerm) ||
                     project.project.project_short_name?.toLowerCase().includes(searchTerm);
            }
            return false;

          case 'project_start_date':
            if (condition.operator === 'between' && Array.isArray(condition.value)) {
              const startDate = project.project.project_start_date;
              if (!startDate) return false;
              const projectDate = new Date(startDate);
              const fromDate = new Date(condition.value[0]);
              const toDate = new Date(condition.value[1]);
              return projectDate >= fromDate && projectDate <= toDate;
            }
            return false;

          default:
            return true;
        }
      });
    }).length;
  }

  // 加载字典项
  async function loadDictionaryItems() {
    try {
      // 加载疾病字典
      const diseasesDict = await sqliteDictionaryService.getDictByName('疾病');
      console.log('疾病字典:', diseasesDict);
      if (diseasesDict && diseasesDict.items) {
        diseases = diseasesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('加载的疾病列表:', diseases);
      }

      // 加载项目阶段字典
      console.log('开始加载研究分期字典...');
      try {
        const stagesDict = await sqliteDictionaryService.getDictByName('研究分期');
        console.log('研究分期字典详情:', JSON.stringify(stagesDict, null, 2));

        if (stagesDict && stagesDict.items) {
          console.log('研究分期字典项数量:', stagesDict.items.length);
          console.log('研究分期字典项示例:', stagesDict.items.slice(0, 3));

          stages = stagesDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
          console.log('加载的研究分期列表:', stages);
        } else {
          console.error('研究分期字典为空或没有items属性');
        }
      } catch (err) {
        console.error('加载研究分期字典时出错:', err);
      }

      // 加载项目状态字典（使用研究阶段字典）
      console.log('开始加载项目状态字典...');
      try {
        // 直接加载"研究阶段"字典
        const statusesDict = await sqliteDictionaryService.getDictByName('研究阶段');
        console.log('研究阶段字典详情:', JSON.stringify(statusesDict, null, 2));

        if (statusesDict && statusesDict.items) {
          console.log('研究阶段字典项数量:', statusesDict.items.length);
          console.log('研究阶段字典项示例:', statusesDict.items.slice(0, 3));

          statuses = statusesDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
          console.log('加载的研究阶段列表:', statuses);
        } else {
          console.error('研究阶段字典为空或没有items属性');
          // 如果无法加载，则使用空数组
          statuses = [];
        }
      } catch (err) {
        console.error('加载研究阶段字典时出错:', err);
        // 出错时使用空数组
        statuses = [];
      }

      // 加载招募状态字典
      console.log('开始加载招募状态字典...');
      try {
        // 首先尝试加载"招募状态"字典
        let recruitmentStatusesDict = await sqliteDictionaryService.getDictByName('招募状态');
        console.log('招募状态字典详情:', JSON.stringify(recruitmentStatusesDict, null, 2));

        // 如果"招募状态"字典不存在或为空，则尝试加载"招募公司"字典作为备用
        if (!recruitmentStatusesDict || !recruitmentStatusesDict.items || recruitmentStatusesDict.items.length === 0) {
          console.log('招募状态字典为空，尝试加载招募公司字典作为备用...');
          recruitmentStatusesDict = await sqliteDictionaryService.getDictByName('招募公司');
          console.log('招募公司字典详情(作为招募状态备用):', JSON.stringify(recruitmentStatusesDict, null, 2));
        }

        if (recruitmentStatusesDict && recruitmentStatusesDict.items) {
          console.log('招募状态字典项数量:', recruitmentStatusesDict.items.length);
          console.log('招募状态字典项示例:', recruitmentStatusesDict.items.slice(0, 3));

        recruitmentStatuses = recruitmentStatusesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('加载的招募状态列表:', recruitmentStatuses);
        } else {
          console.error('招募状态字典为空或没有items属性');
          // 如果仍然无法加载，则使用空数组
          recruitmentStatuses = [];
        }
      } catch (err) {
        console.error('加载招募状态字典时出错:', err);
        // 出错时使用空数组
        recruitmentStatuses = [];
      }

      // 加载申办方字典
      try {
        const sponsorsDict = await sqliteDictionaryService.getDictByName('申办方');
        if (sponsorsDict && sponsorsDict.items) {
          sponsors = sponsorsDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
        }
      } catch (err) {
        console.error('加载申办方字典失败:', err);
        sponsors = [];
      }
    } catch (err) {
      console.error('加载字典项失败:', err);
    }
  }

  // 初始化项目管理表
  async function initProjectTables() {
    try {
      await projectManagementService.initTables();
      console.log('项目管理表初始化成功');
    } catch (err) {
      console.error('项目管理表初始化失败:', err);
    }
  }

  // 初始化筛选配置表
  async function initFilterConfigTables() {
    try {
      await filterConfigService.initTables();
      console.log('筛选配置表初始化成功');
    } catch (err) {
      console.error('筛选配置表初始化失败:', err);
    }
  }

  function applyFilters(options: { resetPage?: boolean } = {}) {
    if (options.resetPage !== false) {
      currentPage = 1;
    }
    loadProjects();
  }

  // 处理搜索
  function handleSearch() {
    applyFilters();
  }

  function getRoleBadgeClass(roleName: string | undefined): string {
    if (!roleName) {
      return 'border-gray-200 bg-gray-100 text-gray-700';
    }

    const normalized = roleName.toLowerCase();

    if (normalized.includes('研究者') || normalized.includes('pi')) {
      return 'border-blue-200 bg-blue-50 text-blue-700';
    }
    if (normalized.includes('协调') || normalized.includes('crc')) {
      return 'border-purple-200 bg-purple-50 text-purple-700';
    }
    if (normalized.includes('监查') || normalized.includes('cra')) {
      return 'border-amber-200 bg-amber-50 text-amber-700';
    }
    if (normalized.includes('护士') || normalized.includes('护理')) {
      return 'border-teal-200 bg-teal-50 text-teal-700';
    }
    if (normalized.includes('项目') || normalized.includes('pm')) {
      return 'border-sky-200 bg-sky-50 text-sky-700';
    }

    return 'border-gray-200 bg-gray-100 text-gray-700';
  }

  async function handleViewInclusionCriteria(project: ProjectCardView) {
    const projectId = project?.project?.project_id;
    if (!projectId) {
      return;
    }

    criteriaModalProjectName = project.project.project_short_name || project.project.project_name || '入组标准';
    criteriaModalProjectId = projectId;
    showCriteriaModal = true;
    criteriaModalLoading = true;
    criteriaModalError = null;
    criteriaModalItems = [];

    try {
      const allCriteria = await getProjectCriteriaDetails(projectId);
      criteriaModalItems = allCriteria.filter(item => item?.criterion?.criterion_type === 'inclusion');
    } catch (err) {
      criteriaModalError = err instanceof Error ? err.message : '加载入组标准失败';
    } finally {
      criteriaModalLoading = false;
    }
  }

  function handleCloseCriteriaModal() {
    showCriteriaModal = false;
    criteriaModalItems = [];
    criteriaModalError = null;
    criteriaModalProjectId = null;
  }

  function toggleActionMenu(event: MouseEvent, projectId: string | undefined) {
    event.stopPropagation();
    if (!projectId) {
      return;
    }
    actionMenuProjectId = actionMenuProjectId === projectId ? null : projectId;
  }

  function closeActionMenu() {
    actionMenuProjectId = null;
  }

  function handleMenuAreaClick(event: MouseEvent) {
    event.stopPropagation();
  }

  function handleGlobalClick() {
    closeActionMenu();
  }

  onMount(() => {
    window.addEventListener('click', handleGlobalClick);
  });

  onDestroy(() => {
    window.removeEventListener('click', handleGlobalClick);
  });

  function removeFilter(type: 'disease' | 'stage' | 'status' | 'recruitment' | 'sponsor' | 'pi' | 'search' | 'pinned') {
    switch (type) {
      case 'disease':
        selectedDiseaseId = null;
        break;
      case 'stage':
        selectedStageId = null;
        break;
      case 'status':
        selectedStatusId = null;
        break;
      case 'recruitment':
        selectedRecruitmentStatusId = null;
        break;
      case 'sponsor':
        selectedSponsorIds = [];
        break;
      case 'pi':
        selectedPiIds = [];
        break;
      case 'search':
        searchName = '';
        break;
      case 'pinned':
        onlyPinned = false;
        break;
    }

    applyFilters();
  }

  // 处理状态卡片点击
  function handleStatusCardClick(filter: 'all' | 'ongoing' | 'finished' | 'recruiting') {
    // 更新当前活动的过滤器
    activeFilter = filter;



    // 重置页码
    currentPage = 1;

    // 根据过滤器设置相应的状态ID
    switch (filter) {
      case 'all':
        // 显示所有项目，清除状态过滤
        selectedStatusId = null;
        selectedRecruitmentStatusId = null;
        break;
      case 'ongoing':
        // 查找"在研"状态的项目ID
        const ongoingStatus = statuses.find(s => s.item_value === '在研');
        selectedStatusId = ongoingStatus?.item_id || null;
        selectedRecruitmentStatusId = null;
        break;
      case 'finished':
        // 查找"已结束"状态的项目ID
        const finishedStatus = statuses.find(s => s.item_value === '已结束');
        selectedStatusId = finishedStatus?.item_id || null;
        selectedRecruitmentStatusId = null;
        break;
      case 'recruiting':
        // 查找"招募中"状态的项目ID
        const recruitingStatus = recruitmentStatuses.find(s => s.item_value === '招募中');
        selectedStatusId = null; // 清除研究阶段过滤
        selectedRecruitmentStatusId = recruitingStatus?.item_id || null;
        break;
    }

    // 加载过滤后的项目（不更新统计数据）
    applyFilters({ resetPage: false });
  }


  // 筛选配置相关函数
  async function loadFilterConfigs() {
    try {
      filterConfigs = await filterConfigService.getFilterConfigs();
      updateCustomFilterTags();
    } catch (error) {
      console.error('加载筛选配置失败:', error);
    }
  }

  async function saveFilterConfig(config: FilterConfig) {
    try {
      if (currentFilterConfig?.id) {
        // 更新现有配置
        await filterConfigService.updateFilterConfig(currentFilterConfig.id, config);
      } else {
        // 创建新配置
        await filterConfigService.saveFilterConfig(config);
      }

      // 重新加载配置列表
      await loadFilterConfigs();

      // 关闭对话框
      showFilterConfigDialog = false;
      currentFilterConfig = null;
    } catch (error) {
      console.error('保存筛选配置失败:', error);
      alert('保存失败，请重试');
    }
  }

  async function deleteFilterConfig(id: string) {
    try {
      await filterConfigService.deleteFilterConfig(id);
      await loadFilterConfigs();
    } catch (error) {
      console.error('删除筛选配置失败:', error);
      alert('删除失败，请重试');
    }
  }

  function updateCustomFilterTags() {
    // 基础列表（按照 filterConfigs 原始顺序）
    const baseList = filterConfigs.map(config => {
      // 计算该筛选配置对应的项目数量
      const count = calculateProjectCountForFilter(config);

      return {
        id: config.id || '',
        label: config.name,
        color: 'gray',
        isActive: false,
        count,
        filterConfig: config,
        onClick: () => applyFilterConfig(config)
      };
    });

    // 获取当前内存顺序与本地持久化顺序
    let currentOrderIds: string[] = Array.isArray(customFilterTags) ? customFilterTags.map(t => t.id) : [];
    let savedOrderIds: string[] = [];
    try {
      const saved = localStorage.getItem('customFilterTagsOrder');
      if (saved) savedOrderIds = JSON.parse(saved);
    } catch (e) {
      console.warn('[快捷筛选] 读取本地排序失败:', e);
    }

    // 构建 id => tag 映射
    const map = new Map(baseList.map(t => [t.id, t] as const));

    // 优先使用当前顺序，其次使用本地存储顺序
    const preferredOrder = (currentOrderIds && currentOrderIds.length > 0)
      ? currentOrderIds
      : savedOrderIds;

    let ordered: typeof baseList = [];
    if (preferredOrder && preferredOrder.length > 0) {
      // 按照 preferredOrder 排序，忽略已不存在的ID
      for (const id of preferredOrder) {
        const item = map.get(id);
        if (item) ordered.push(item);
      }
      // 追加新出现的项
      for (const item of baseList) {
        if (!ordered.find(t => t.id === item.id)) ordered.push(item);
      }
    } else {
      ordered = baseList;
    }

    console.log('[快捷筛选] updateCustomFilterTags 应用排序：', ordered.map(t => t.id));
    customFilterTags = ordered;
  }

  function applyFilterConfig(config: FilterConfig) {
    // 应用筛选配置到当前查询
    const query = filterConfigService.applyFilterConfigToQuery(config);
    console.log('[快捷筛选] 点击标签，配置:', config);
    console.log('[快捷筛选] 解析得到查询参数:', query);

    // 判断是否包含后端支持的筛选字段
    const hasSupportedFilter = !!(
      query.name ||
      query.disease_item_id ||
      query.project_stage_item_id ||
      query.project_status_item_id ||
      query.recruitment_status_item_id ||
      (Array.isArray(query.sponsor_item_ids) && query.sponsor_item_ids.length) ||
      (Array.isArray(query.pi_personnel_ids) && query.pi_personnel_ids.length)
    );

    if (!hasSupportedFilter) {
      console.warn('[快捷筛选] 此筛选配置不包含当前列表支持的字段（项目名称/疾病/研究分期/项目状态/招募状态/申办方/PI），列表结果可能不变化');
    }

    // 更新状态
    selectedDiseaseId = query.disease_item_id || null;
    selectedStageId = query.project_stage_item_id || null;
    selectedStatusId = query.project_status_item_id || null;
    selectedRecruitmentStatusId = query.recruitment_status_item_id || null;
    selectedSponsorIds = query.sponsor_item_ids ? query.sponsor_item_ids.map(String) : [];
    selectedPiIds = query.pi_personnel_ids ? query.pi_personnel_ids.map(String) : [];
    searchName = query.name || '';

    // 重置页码并加载项目
    currentPage = 1;
    loadProjects();

    // 更新标签状态
    updateCustomFilterTags();
    customFilterTags = customFilterTags.map(tag => ({
      ...tag,
      isActive: tag.filterConfig?.id === config.id
    }));
  }

  // 按角色分组人员
  function groupPersonnelByRole(personnel: ProjectPersonnelWithDetails[]) {
    if (!personnel || personnel.length === 0) return [];

    const roleGroups: {
      roleId: number;
      roleName: string;
      personnel: Array<{
        index: number;
        person: ProjectPersonnelWithDetails;
      }>;
    }[] = [];

    // 遍历所有人员，按角色分组
    personnel.forEach((person, index) => {
      const roleId = person.role_item_id;
      const roleName = person.role?.item_value || '未知角色';

      // 查找该角色是否已存在于分组中
      let roleGroup = roleGroups.find(group => group.roleId === roleId);

      if (!roleGroup) {
        // 如果角色不存在，创建新的角色分组
        roleGroup = {
          roleId,
          roleName,
          personnel: []
        };
        roleGroups.push(roleGroup);
      }

      // 将人员添加到对应角色分组
      roleGroup.personnel.push({
        index,
        person
      });
    });

    // 按角色名称排序
    roleGroups.sort((a, b) => a.roleName.localeCompare(b.roleName));

    return roleGroups;
  }

  const CRITICAL_ROLES = ['主要研究者', '临床协调员(CRC)'];

  function getCriticalRoleChips(project: ProjectCardView) {
    const personnel = project.personnel ?? [];
    const groups = groupPersonnelByRole(personnel);
    return CRITICAL_ROLES.map(role => {
      const group = groups.find(g => g.roleName === role);
      return {
        role,
        names: group
          ? group.personnel.map(({ person }) => person.personnel?.name || '未知')
          : []
      };
    });
  }

  function getMissingCriticalRoles(project: ProjectCardView): string[] {
    const personnel = project.personnel ?? [];
    const groups = groupPersonnelByRole(personnel);
    const assigned = groups.map(g => g.roleName);
    return CRITICAL_ROLES.filter(role => !assigned.includes(role));
  }



  // 处理页码变化
  function handlePageChange(page: number) {
    currentPage = page;
    loadProjects();
  }

  // 处理查看项目
  function handleViewProject(projectId: string) {
    // 保存当前的筛选状态到 store
    saveFilterState({
      activeFilter,
      selectedDiseaseFilter: null,
      searchName,
      sortBy,
      sortOrder: sortOrder as 'asc' | 'desc',
      currentPage,
      selectedDiseaseId,
      selectedStageId,
      selectedStatusId,
      selectedRecruitmentStatusId,
      activeCardId: null
    });

    goto(`/projects/${projectId}`);
  }

  // 处理编辑项目
  function handleEditProject(projectId: string) {
    console.log('编辑项目，项目ID:', projectId);
    if (!projectId) {
      console.error('无效的项目ID');
      error = '无效的项目ID';
      return;
    }
    goto(`/projects/${projectId}/edit`);
  }

  // 处理删除项目
  function handleDeleteProject(projectId: string) {
    // 查找要删除的项目
    const project = projects.find(p => p.project.project_id === projectId);
    if (!project) {
      error = "找不到要删除的项目";
      return;
    }

    // 设置要删除的项目信息
    projectToDelete = {
      id: projectId,
      name: project.project.project_short_name || project.project.project_name
    };

    // 显示确认对话框
    showDeleteConfirm = true;
  }

  // 确认删除项目
  async function confirmDeleteProject() {
    if (!projectToDelete) return;

    // 验证输入的项目简称是否匹配
    if (confirmProjectName !== projectToDelete.name) {
      deleteConfirmError = "输入的项目简称不匹配，请重新输入";
      return;
    }

    isDeleting = true;
    error = null;
    deleteConfirmError = null;

    try {
      await projectManagementService.deleteProject(projectToDelete.id);
      showDeleteConfirm = false;
      projectToDelete = null;
      confirmProjectName = '';

      // 删除项目后，重新加载所有项目以更新统计数据
      await loadAllProjects();
      // 然后加载当前筛选的项目列表
      loadProjects();
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isDeleting = false;
    }
  }

  // 取消删除项目
  function cancelDeleteProject() {
    showDeleteConfirm = false;
    projectToDelete = null;
    confirmProjectName = '';
    deleteConfirmError = null;
  }

  // 计算已启动天数
  function calculateDaysSinceStart(startDate: string | undefined): number | null {
    if (!startDate) return null;
    try {
      const start = new Date(startDate);
      const now = new Date();
      // 检查日期是否有效
      if (isNaN(start.getTime())) {
        return null;
      }
      // 计算天数差
      const diffTime = now.getTime() - start.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch (error) {
      console.error('计算启动天数时出错:', error);
      return null;
    }
  }

  function mapWithStartDays(items: ProjectWithDetails[]): ProjectCardView[] {
    return items.map(item => ({
      ...item,
      startDays: item.project.project_start_date
        ? calculateDaysSinceStart(item.project.project_start_date)
        : null
    }));
  }

  // 处理打开项目文件夹
  async function handleOpenProjectFolder(projectPath: string | undefined) {
    if (!projectPath) {
      error = "项目路径不存在";
      return;
    }

    try {
      const response = await fileSystemService.openFolder(projectPath);
      if (!response.success) {
        error = response.error || "打开项目文件夹失败";
      }
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    }
  }

  // 处理配置入排标准
  function handleConfigureCriteria(projectId: string) {
    if (!projectId) {
      console.error('无效的项目ID');
      error = '无效的项目ID';
      return;
    }
    goto(`/projects/${projectId}/criteria`);
  }

  // 获取项目入排标准详细内容
  async function getProjectCriteriaDetails(projectId: string): Promise<any[]> {
    // 检查缓存
    if (criteriaCache.has(projectId)) {
      return criteriaCache.get(projectId)!;
    }

    try {
      // 获取入组标准
      const inclusionCriteria = await ruleDesignerService.getProjectCriteria({
        project_id: projectId,
        criterion_type: 'inclusion'
      });

      // 获取排除标准
      const exclusionCriteria = await ruleDesignerService.getProjectCriteria({
        project_id: projectId,
        criterion_type: 'exclusion'
      });

      // 合并所有标准
      const allCriteria = [...inclusionCriteria, ...exclusionCriteria];

      // 缓存结果
      criteriaCache.set(projectId, allCriteria);

      return allCriteria;
    } catch (error) {
      console.error('获取项目入排标准失败:', error);
      return [];
    }
  }

  // 获取规则的友好显示名称
  function getRuleFriendlyName(ruleDefinition: any): string {
    // 优先使用 label，如果 label 为空或不存在，则使用 rule_name
    return ruleDefinition.label && ruleDefinition.label.trim()
      ? ruleDefinition.label
      : ruleDefinition.rule_name;
  }

  // 格式化参数值显示（使用与项目详情页面相同的逻辑）
  function formatParameterValues(criterion: any): string {
    try {
      if (!criterion.rule_definition.parameter_schema) {
        return '无参数定义';
      }

      if (!criterion.criterion.parameter_values) {
        return '无参数值';
      }

      const schema = ruleDesignerService.parseParameterSchema(criterion.rule_definition.parameter_schema);
      const values = ruleDesignerService.parseParameterValues(criterion.criterion.parameter_values);

      if (!schema.parameters || !Array.isArray(schema.parameters) || schema.parameters.length === 0) {
        return '无参数定义';
      }

      const formattedValues = schema.parameters.map(param => {
        const value = values[param.name];

        if (value === undefined || value === null) return null;

        let displayValue = value;
        if (param.unit) {
          displayValue = `${value} ${param.unit}`;
        }

        return `${param.label}: ${displayValue}`;
      }).filter(Boolean);

      return formattedValues.join(', ');
    } catch (e) {
      console.error('格式化参数值失败:', e);
      return '参数格式错误';
    }
  }

  // 组件挂载时初始化
  onMount(async () => {
    await initProjectTables();
    await initFilterConfigTables();
    await loadDictionaryItems();

    // 预加载 PI 列表（可按需调整为搜索框）
    try {
      const staff = await staffService.getAllStaff();
      piStaff = staff.map(s => ({ id: s.id as number, name: s.name }));

      const resolvedStaffId = resolveStoredStaffId(piStaff);
      if (activeStaffId !== resolvedStaffId) {
        setActiveStaffId(resolvedStaffId, { silent: true });
      }
    } catch (e) {
      console.warn('加载人员列表失败（用于置顶功能）:', e);
    }

    await loadFilterConfigs();

    // 首先加载所有项目以获取统计数据
    await loadAllProjects();

    // 总是应用默认设置：在研项目 + 按项目简称倒序排列
    activeFilter = 'ongoing';
    sortBy = 'project_short_name';
    sortOrder = 'desc';
    currentPage = 1;

    // 清除其他筛选条件，只保留"在研项目"筛选
    searchName = '';
    selectedDiseaseId = null;
    selectedStageId = null;
    selectedRecruitmentStatusId = null;

    // 应用"在研项目"筛选
    handleStatusCardClick('ongoing');
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="space-y-6">
    <div class="bg-white rounded-lg shadow p-5">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex items-center gap-3">
          <div class="bg-blue-600 p-2 rounded-lg text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-800">项目管理</h1>
            <p class="text-sm text-gray-500">管理研究项目、人员和申办方</p>
          </div>
        </div>
        <div class="flex flex-wrap items-center gap-2 justify-end">
          <div class="relative w-64">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search class="h-4 w-4 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="搜索项目名称..."
              bind:value={searchName}
              class="pl-9"
              onkeydown={(event) => {
                const keyboardEvent = event as unknown as KeyboardEvent;
                if (keyboardEvent.key === 'Enter') handleSearch();
              }}
            />
            {#if searchName}
              <button
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                onclick={() => removeFilter('search')}
                aria-label="清除搜索"
                title="清除搜索"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            {/if}
          </div>
          <Button
            variant="outline"
            size="sm"
            class="h-10 px-3"
            onclick={handleSearch}
          >
            搜索
          </Button>
          <Button
            variant={onlyPinned ? 'default' : 'outline'}
            size="sm"
            class={onlyPinned ? 'border-amber-500 bg-amber-500 text-white hover:bg-amber-600' : 'border-amber-300 text-amber-600 hover:bg-amber-50'}
            onclick={togglePinnedFilter}
          >
            <Star
              class="mr-1 h-4 w-4"
              fill={onlyPinned ? '#ffffff' : 'none'}
              stroke={onlyPinned ? '#ffffff' : 'currentColor'}
              stroke-width={onlyPinned ? 0 : 1.8}
            />
            置顶项目
          </Button>
          {#if activeStaffId !== null}
            <div class={`flex items-center gap-1 rounded-full border px-2 py-1 text-xs font-medium ${pinnedLimitReached ? 'border-red-200 bg-red-50 text-red-600' : 'border-amber-200 bg-amber-50 text-amber-700'}`}>
              <Star
                class="h-3 w-3"
                fill={pinnedLimitReached ? '#dc2626' : '#f97316'}
                stroke={pinnedLimitReached ? '#dc2626' : '#f97316'}
              />
              <span>{pinnedCount}/{PIN_LIMIT}</span>
            </div>
          {/if}
          <Button variant="outline" class="border-gray-300 hover:bg-gray-50" onclick={async () => {
            await loadAllProjects();
            loadProjects();
          }}>
            <RefreshCw class="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button variant="outline" class="border-gray-300 hover:bg-gray-50" onclick={() => showExportDialog = true}>
            <FileDown class="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button variant="outline" class="border-gray-300 hover:bg-gray-50" onclick={() => showCsvImportDialog = true}>
            <Upload class="mr-2 h-4 w-4" />
            批量导入授权人员
          </Button>
          <Button class="bg-blue-600 hover:bg-blue-700" onclick={() => goto('/projects/new')}>
            <PlusCircle class="mr-2 h-4 w-4" />
            新建项目
          </Button>
        </div>
      </div>
      {#if pinError}
        <div class="mt-3 rounded-md border border-red-200 bg-red-50 px-3 py-2 text-sm text-red-700">
          {pinError}
        </div>
      {/if}
    </div>


    <div class="bg-white rounded-lg border border-gray-200 p-4">
      <div class="flex flex-col gap-4">
        <!-- 统一筛选界面 -->
        <div class="flex flex-wrap items-start justify-between gap-4">
          <div class="flex flex-col gap-3 flex-1 min-w-0">
            <div class="flex items-center gap-2">
              <h3 class="text-sm font-semibold text-gray-800">项目筛选</h3>
              <span class="text-xs text-gray-500">快速筛选和自定义组合</span>
            </div>
            <div class="flex flex-wrap gap-2" role="list">
              <!-- 基础状态筛选 -->
              <StatsBadge
                title="全部项目"
                count={projectStats.totalProjects}
                color="blue"
                selectable
                active={activeFilter === 'all'}
                onClick={() => handleStatusCardClick('all')}
              />
              <StatsBadge
                title="在研项目"
                count={projectStats.ongoingProjects}
                color="green"
                selectable
                active={activeFilter === 'ongoing'}
                onClick={() => handleStatusCardClick('ongoing')}
              />
              <StatsBadge
                title="已结束项目"
                count={projectStats.finishedProjects}
                color="purple"
                selectable
                active={activeFilter === 'finished'}
                onClick={() => handleStatusCardClick('finished')}
              />
              <StatsBadge
                title="招募中项目"
                count={projectStats.recruitingProjects}
                color="orange"
                selectable
                active={activeFilter === 'recruiting'}
                onClick={() => handleStatusCardClick('recruiting')}
              />

              <!-- 自定义快捷筛选 -->
              {#if customFilterTags.length > 0}
                {#each customFilterTags as tag, index (tag.id)}
                  <QuickFilterTag
                    {tag}
                    showActions={editMode}
                    canMoveUp={index > 0}
                    canMoveDown={index < customFilterTags.length - 1}
                    onMoveUp={moveTagUp}
                    onMoveDown={moveTagDown}
                    onEdit={(tag) => {
                      currentFilterConfig = tag.filterConfig || null;
                      showFilterConfigDialog = true;
                    }}
                    onDelete={(tag) => {
                      if (tag.filterConfig?.id && confirm(`确定要删除筛选配置"${tag.label}"吗？`)) {
                        deleteFilterConfig(tag.filterConfig.id);
                      }
                    }}
                  />
                {/each}
              {/if}
            </div>
            {#if customFilterTags.length === 0}
              <div class="text-xs text-gray-400 mt-1">
                暂无自定义筛选，点击右侧按钮进行创建。
              </div>
            {/if}
          </div>
          <div class="flex items-center gap-2 flex-shrink-0">
            <button
              type="button"
              class="text-xs font-medium text-blue-600 hover:text-blue-800"
              onclick={() => showFilterConfigDialog = true}
            >
              + 自定义筛选
            </button>
            <button
              type="button"
              class="text-xs font-medium text-gray-600 hover:text-gray-800"
              aria-pressed={editMode}
              onclick={() => editMode = !editMode}
            >
              {editMode ? '完成' : '排序/编辑'}
            </button>
          </div>
        </div>
      </div>
    </div>

    {#if error}
      <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
      </div>
    {/if}

  <!-- 项目列表 -->
  <div class="mt-4">
    {#if isLoading}
      <div class="bg-white rounded-lg border border-gray-200 p-6 text-gray-500 flex justify-center">
        <div class="flex items-center gap-2">
          <div class="h-5 w-5 animate-spin border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span>加载项目中...</span>
        </div>
      </div>
    {:else if projects.length === 0}
      <div class="bg-white rounded-lg border border-dashed border-gray-300 p-10 text-center text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto mb-3 h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-sm">暂无项目数据</p>
        <p class="mt-1 text-xs text-gray-400">尝试调整筛选条件或新建项目</p>
      </div>
    {:else}
      <div class="grid gap-4 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
        {#each projects as project (project.project.project_id)}
          <article>
            <div
              class="group relative flex h-full flex-col gap-3 rounded-xl border border-gray-200 bg-white p-4 text-sm shadow-sm transition hover:border-blue-300 hover:shadow cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-200"
              class:border-amber-300={project.project.is_pinned}
              class:bg-amber-50={project.project.is_pinned}
              class:shadow-md={project.project.is_pinned}
              role="button"
              tabindex="0"
              onclick={() => handleViewProject(project.project.project_id || '')}
              onkeydown={(event) => {
                const keyboardEvent = event as unknown as KeyboardEvent;
                if (keyboardEvent.key === 'Enter' || keyboardEvent.key === ' ') {
                  keyboardEvent.preventDefault();
                  handleViewProject(project.project.project_id || '');
                }
              }}
            >

              <div class="space-y-3">
                <div class="min-w-0 space-y-2 pr-12">
                  <div class="space-y-1">
                    <h2 class="text-sm md:text-base font-semibold text-gray-900 leading-tight break-words">
                      {project.project.project_short_name?.trim() || '未命名项目'}
                    </h2>
                    {#if project.project.project_name?.trim()}
                      <p class="text-[10px] md:text-xs text-gray-500 leading-snug break-words" title={project.project.project_name}>
                        {project.project.project_name}
                      </p>
                    {/if}
                  </div>
                  {#if project.project.drug_introduction}
                    <p class="text-[11px] leading-5 text-gray-500 line-clamp-2">
                      {project.project.drug_introduction}
                    </p>
                  {/if}
                </div>

                <div class="flex flex-wrap items-center gap-1.5 text-xs text-gray-600">
                  <span class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-1 text-blue-800 font-medium">
                    {project.disease?.item_value || '疾病未设置'}
                  </span>
                  {#if project.project.drug_mechanism}
                    <span class="inline-flex items-center rounded-full bg-purple-50 px-2 py-0.5 text-purple-700 text-[11px]">
                      {project.project.drug_mechanism}
                    </span>
                  {/if}
                  {#if project.project_status?.item_value}
                    <span class="inline-flex items-center rounded-full bg-blue-50 px-2 py-0.5 text-blue-700">
                      {project.project_status?.item_value}
                    </span>
                  {/if}
                  {#if project.recruitment_status?.item_value}
                    <span class="inline-flex items-center rounded-full bg-amber-50 px-2 py-0.5 text-amber-700">
                      {project.recruitment_status?.item_value}
                    </span>
                  {/if}
                  {#if project.project_stage?.item_value}
                    <span class="inline-flex items-center rounded-full bg-emerald-50 px-2 py-0.5 text-emerald-700">
                      {project.project_stage?.item_value}
                    </span>
                  {/if}
                </div>

                <div class="flex flex-wrap items-center gap-1.5 text-xs text-gray-600">
                  {#if project.startDays !== null}
                    <span
                      class="inline-flex items-center gap-1 rounded-full bg-indigo-50 px-2 py-0.5 text-indigo-700"
                      title={project.project.project_start_date ? `启动时间：${project.project.project_start_date.slice(0, 10).replace(/-/g, '/')}` : ''}
                    >
                      <RefreshCw class="h-3 w-3" /> {project.startDays} 天
                    </span>
                  {:else}
                    <span class="inline-flex items-center gap-1 rounded-full border border-dashed border-indigo-200 px-2 py-0.5 text-indigo-500">
                      未设置启动时间
                    </span>
                  {/if}
                  {#if project.criteria_stats?.has_criteria}
                    <span class="inline-flex items-center gap-1 rounded-full bg-blue-50 px-2 py-0.5 text-blue-700">
                      标准 {project.criteria_stats.inclusion_count}/{project.criteria_stats.exclusion_count}
                    </span>
                  {:else}
                    <span class="inline-flex items-center gap-1 rounded-full border border-dashed border-blue-200 px-2 py-0.5 text-blue-500">
                      未配置标准
                    </span>
                  {/if}
                  {#if project.has_recruitment_companies !== undefined && project.has_recruitment_companies !== null}
                    <span
                      class={`inline-flex items-center justify-center rounded-full px-2 py-0.5 ${project.has_recruitment_companies ? 'bg-teal-50 text-teal-700' : 'bg-gray-100 text-gray-600'}`}
                      title={project.has_recruitment_companies ? '含招募公司' : '无招募公司'}
                      aria-label={project.has_recruitment_companies ? '含招募公司' : '无招募公司'}
                    >
                      {#if project.has_recruitment_companies}
                        <Building2 class="h-3.5 w-3.5" />
                        <span class="sr-only">含招募公司</span>
                      {:else}
                        <CircleSlash class="h-3.5 w-3.5" />
                        <span class="sr-only">无招募公司</span>
                      {/if}
                    </span>
                  {/if}
                </div>

                <div class="text-[11px] leading-5 text-gray-500">
                  {#if project.sponsors && project.sponsors.length > 0}
                    <span class="text-gray-700 line-clamp-1" title={project.sponsors.map(s => s.sponsor?.item_value || '-').join('、')}>
                      {project.sponsors[0].sponsor?.item_value || '-'}
                      {#if project.sponsors.length > 1}
                        <span class="text-gray-400"> 等{project.sponsors.length}家</span>
                      {/if}
                    </span>
                  {:else}
                    <span class="text-gray-400">申办方未设置</span>
                  {/if}
                </div>
              </div>

              <div class="absolute right-3 top-3 flex items-center gap-1">
                <button
                      class="rounded-full border border-transparent p-1 text-gray-400 transition hover:bg-amber-50 hover:text-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-200 disabled:cursor-not-allowed disabled:opacity-60"
                      aria-label={project.project.is_pinned ? '取消置顶' : '置顶项目'}
                      title={project.project.is_pinned ? '取消置顶' : (activeStaffId === null ? '暂无可用的项目人员，无法置顶' : '置顶项目')}
                  onclick={(event) => handleTogglePin(event as MouseEvent, project)}
                  disabled={isPinBusy(project.project.project_id) || activeStaffId === null}
                >
                  <Star
                    class="h-4 w-4"
                    stroke={project.project.is_pinned ? '#f97316' : 'currentColor'}
                    stroke-width={project.project.is_pinned ? 0 : 1.8}
                    fill={project.project.is_pinned ? '#facc15' : 'none'}
                  />
                </button>
                <div class="relative flex shrink-0 items-center" onclick={(event) => handleMenuAreaClick(event as MouseEvent)}>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-500 hover:bg-gray-100 hover:text-gray-700"
                    title="更多操作"
                    onclick={(event) => toggleActionMenu(event as MouseEvent, project.project.project_id || '')}
                  >
                    <MoreHorizontal class="h-4 w-4" />
                  </Button>
                  {#if actionMenuProjectId === (project.project.project_id || '')}
                    <div class="absolute right-0 top-9 z-30 w-40 rounded-lg border border-gray-200 bg-white p-2 text-xs text-gray-700 shadow-xl">
                      <button
                        type="button"
                        class="flex w-full items-center gap-2 rounded-md px-2 py-1.5 hover:bg-blue-50 hover:text-blue-700"
                        onclick={(event) => {
                          event.stopPropagation();
                          handleEditProject(project.project.project_id || '');
                          closeActionMenu();
                        }}
                      >
                        <Pencil class="h-3.5 w-3.5" />
                        编辑项目
                      </button>
                      <button
                        type="button"
                        class="flex w-full items-center gap-2 rounded-md px-2 py-1.5 hover:bg-purple-50 hover:text-purple-700"
                        onclick={(event) => {
                          event.stopPropagation();
                          handleConfigureCriteria(project.project.project_id || '');
                          closeActionMenu();
                        }}
                      >
                        <ClipboardList class="h-3.5 w-3.5" />
                        配置入排标准
                      </button>
                      <button
                        type="button"
                        class="flex w-full items-center gap-2 rounded-md px-2 py-1.5 hover:bg-blue-50 hover:text-blue-700"
                        onclick={(event) => {
                          event.stopPropagation();
                          handleViewInclusionCriteria(project);
                          closeActionMenu();
                        }}
                      >
                        <Eye class="h-3.5 w-3.5" />
                        查看入组标准
                      </button>
                      <button
                        type="button"
                        class="flex w-full items-center gap-2 rounded-md px-2 py-1.5 hover:bg-green-50 hover:text-green-700"
                        onclick={(event) => {
                          event.stopPropagation();
                          handleOpenProjectFolder(project.project.project_path);
                          closeActionMenu();
                        }}
                      >
                        <Folder class="h-3.5 w-3.5" />
                        打开项目文件夹
                      </button>
                      <button
                        type="button"
                        class="flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-red-600 hover:bg-red-50"
                        onclick={(event) => {
                          event.stopPropagation();
                          handleDeleteProject(project.project.project_id || '');
                          closeActionMenu();
                        }}
                      >
                        <Trash class="h-3.5 w-3.5" />
                        删除项目
                      </button>
                    </div>
                  {/if}
                </div>
              </div>

              <div class="mt-2 flex flex-col gap-2 border-t border-gray-100 pt-3 text-xs text-gray-600">
                {#if project.personnel && project.personnel.length > 0}
                  <div class="flex flex-wrap gap-1">
                    {#each getCriticalRoleChips(project) as chip}
                      {#if chip.names.length > 0}
                        {#each chip.names as name}
                          <span
                            class={`inline-flex items-center rounded-full border px-2 py-0.5 text-[11px] font-medium ${getRoleBadgeClass(chip.role)}`}
                            title={chip.role}
                          >
                            {name}
                          </span>
                        {/each}
                      {/if}
                    {/each}
                  </div>
                  {#if getMissingCriticalRoles(project).length > 0}
                    <div class="flex items-center gap-1 text-[11px] text-orange-600">
                      <AlertTriangle class="h-3.5 w-3.5" />
                      <span>缺失 {getMissingCriticalRoles(project).join('、')}</span>
                    </div>
                  {/if}
                {:else}
                  <div class="flex items-center gap-1 text-gray-400">
                    <AlertTriangle class="h-3.5 w-3.5" />
                    <span>暂无人员</span>
                  </div>
                {/if}
              </div>
            </div>
          </article>
        {/each}
      </div>
    {/if}
  </div>
  <!-- 分页 -->
  {#if total > 0}
    <div class="flex justify-between items-center mt-4 bg-white p-3 rounded-lg shadow">
      <div class="flex items-center gap-2">
        <span class="text-sm text-gray-600">每页显示:</span>
        <select
          bind:value={pageSize}
          onchange={() => { currentPage = 1; loadProjects(); }}
          class="h-8 w-20 rounded-md border border-input bg-background px-3 py-1 text-sm"
        >
          {#each [5, 10, 20, 50, 100] as size}
            <option value={size}>{size}</option>
          {/each}
        </select>
        <span class="text-sm text-gray-600">共 <span class="font-medium text-gray-800">{total}</span> 条记录，当前第 <span class="font-medium text-gray-800">{currentPage}</span> 页</span>
      </div>
      <div class="flex gap-1">
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === 1}
          onclick={() => handlePageChange(1)}
          class="h-8 px-2 min-w-[40px]"
        >
          首页
        </Button>
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === 1}
          onclick={() => handlePageChange(currentPage - 1)}
          class="h-8 px-2 min-w-[40px]"
        >
          上一页
        </Button>

        <!-- 页码按钮 -->
        {#if total > 0}
          {#each Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => {
            // 计算显示哪些页码
            let pageNumbers = [];
            const totalPages = Math.ceil(total / pageSize);

            if (totalPages <= 5) {
              // 如果总页数小于等于5，显示所有页码
              pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);
            } else {
              // 否则显示当前页附近的页码
              if (currentPage <= 3) {
                pageNumbers = [1, 2, 3, 4, 5];
              } else if (currentPage >= totalPages - 2) {
                pageNumbers = [totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
              } else {
                pageNumbers = [currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2];
              }
            }

            return pageNumbers[i];
          }) as page}
            <Button
              variant={page === currentPage ? 'default' : 'outline'}
              size="sm"
              onclick={() => handlePageChange(page)}
              class="h-8 w-8 p-0 font-medium"
            >
              {page}
            </Button>
          {/each}
        {/if}

        <Button
          variant="outline"
          size="sm"
          disabled={currentPage * pageSize >= total}
          onclick={() => handlePageChange(currentPage + 1)}
          class="h-8 px-2 min-w-[40px]"
        >
          下一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage * pageSize >= total}
          onclick={() => handlePageChange(Math.ceil(total / pageSize))}
          class="h-8 px-2 min-w-[40px]"
        >
          末页
        </Button>
      </div>
    </div>
  {/if}

  </div>

  <!-- 入组标准查看弹窗 -->
  {#if showCriteriaModal}
    <div class="fixed inset-0 z-40 flex items-center justify-center bg-black/50 px-4" onclick={handleCloseCriteriaModal}>
      <div
        class="relative w-full max-w-2xl rounded-2xl bg-white p-6 shadow-2xl"
        role="dialog"
        aria-modal="true"
        onclick={(event) => event.stopPropagation()}
      >
        <div class="flex items-start justify-between gap-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{criteriaModalProjectName}</h3>
            <p class="mt-1 text-xs text-gray-500">入组标准概览</p>
          </div>
          <button
            type="button"
            class="rounded-full p-1 text-gray-400 transition hover:bg-gray-100 hover:text-gray-600"
            onclick={handleCloseCriteriaModal}
            aria-label="关闭"
          >
            <X class="h-4 w-4" />
          </button>
        </div>

        <div class="mt-4 space-y-3">
          {#if criteriaModalLoading}
            <div class="flex items-center gap-2 text-sm text-gray-500">
              <div class="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
              正在加载入组标准...
            </div>
          {:else if criteriaModalError}
            <div class="rounded-md border border-red-200 bg-red-50 px-3 py-2 text-sm text-red-600">
              {criteriaModalError}
            </div>
          {:else if criteriaModalItems.length === 0}
            <div class="rounded-md border border-dashed border-gray-200 bg-gray-50 px-4 py-6 text-center text-sm text-gray-500">
              暂无入组标准，可点击项目卡片中的“配置入排标准”按钮进行设置。
            </div>
          {:else}
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>共 {criteriaModalItems.length} 条入组标准</span>
              <button
                type="button"
                class="text-blue-600 hover:text-blue-700"
                onclick={() => {
                  handleCloseCriteriaModal();
                  if (criteriaModalProjectId) {
                    handleConfigureCriteria(criteriaModalProjectId);
                  }
                }}
              >
                前往配置
              </button>
            </div>
            <div class="max-h-[60vh] overflow-y-auto space-y-3 pr-1">
              {#each criteriaModalItems as item, index}
                <div class="rounded-xl border border-gray-200 bg-gray-50 px-3 py-2">
                  <div class="flex items-start justify-between gap-2">
                    <div class="flex items-center gap-2 text-sm font-medium text-gray-800">
                      <span class="text-xs text-gray-400">{index + 1}.</span>
                      <span class="leading-tight">{getRuleFriendlyName(item.rule_definition)}</span>
                    </div>
                    {#if item.criterion?.criteria_group_id}
                      <span class="rounded-full bg-sky-100 px-2 py-0.5 text-[11px] font-medium text-sky-700">或组</span>
                    {/if}
                  </div>
                  {#if formatParameterValues(item) && formatParameterValues(item) !== '无参数值'}
                    <p class="mt-1 text-[12px] leading-relaxed text-gray-600">{formatParameterValues(item)}</p>
                  {/if}
                  {#if item.criterion?.notes}
                    <p class="mt-1 text-[11px] text-gray-500">备注：{item.criterion.notes}</p>
                  {/if}
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>
    </div>
  {/if}

  <!-- 删除确认对话框 -->
  {#if showDeleteConfirm && projectToDelete}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">确认删除项目</h3>
        <p class="mb-4">您确定要删除项目 <span class="font-semibold">{projectToDelete.name}</span> 吗？此操作将删除所有相关数据，且不可恢复。</p>

        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                为确认删除，请输入项目简称：<strong>{projectToDelete.name}</strong>
              </p>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <label for="confirmProjectName" class="block text-sm font-medium text-gray-700 mb-1">
            项目简称
          </label>
          <div class="flex gap-2">
            <div class="flex-grow">
              <Input
                id="confirmProjectName"
                type="text"
                placeholder="请输入项目简称以确认删除"
                bind:value={confirmProjectName}
                class={deleteConfirmError ? "border-red-500" : ""}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              class="whitespace-nowrap"
              onclick={() => confirmProjectName = projectToDelete?.name || ''}
            >
              自动填入
            </Button>
          </div>
          {#if deleteConfirmError}
            <p class="mt-1 text-sm text-red-600">{deleteConfirmError}</p>
            <p class="text-xs text-gray-500">正确的项目简称是: <span class="font-mono bg-gray-100 px-1 rounded">{projectToDelete?.name}</span></p>
          {/if}
        </div>

        <div class="flex justify-end gap-2">
          <Button variant="outline" onclick={cancelDeleteProject} disabled={isDeleting}>
            取消
          </Button>
          <Button variant="destructive" onclick={confirmDeleteProject} disabled={isDeleting || !confirmProjectName}>
            {#if isDeleting}
              <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
              删除中...
            {:else}
              确认删除
            {/if}
          </Button>
        </div>
      </div>
    </div>
  {/if}

  <!-- 导出项目对话框 -->
  <ExportProjectsDialog bind:open={showExportDialog} projects={projects} />

  <!-- CSV导入对话框 -->
  <CsvImportDialog
    open={showCsvImportDialog}
    onClose={() => {
      showCsvImportDialog = false;
      // 导入完成后刷新项目列表
      loadAllProjects();
      loadProjects();
    }}
  />

  <!-- 筛选配置对话框 -->
  <FilterConfigDialog
    isOpen={showFilterConfigDialog}
    config={currentFilterConfig}
    availableOptions={{
      project_status_item_id: statuses.map(s => ({ value: s.item_id, label: s.item_value })),
      recruitment_status_item_id: recruitmentStatuses.map(s => ({ value: s.item_id, label: s.item_value })),
      disease_item_id: diseases.map(d => ({ value: d.item_id, label: d.item_value })),
      project_stage_item_id: stages.map(s => ({ value: s.item_id, label: s.item_value })),
      sponsor_item_ids: sponsors.map(s => ({ value: s.item_id, label: s.item_value })),
      pi_personnel_ids: piStaff.map(p => ({ value: p.id, label: p.name }))
    }}
    on:save={(event) => saveFilterConfig(event.detail)}
    on:cancel={() => {
      showFilterConfigDialog = false;
      currentFilterConfig = null;
    }}
  />
</div>
